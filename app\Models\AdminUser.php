<?php

declare(strict_types=1);

namespace App\Models;

use DateTimeImmutable;

/**
 * Admin User Entity
 * Represents an administrative user
 */
final class AdminUser
{
    public function __construct(
        private int $id,
        private string $username,
        private string $passwordHash,
        private string $email,
        private DateTimeImmutable $createdAt,
        private DateTimeImmutable $updatedAt
    ) {}

    public static function create(
        int $id,
        string $username,
        string $passwordHash,
        string $email
    ): self {
        $now = new DateTimeImmutable();

        return new self(
            $id,
            $username,
            $passwordHash,
            $email,
            $now,
            $now
        );
    }

    public static function findByUsername(string $username): ?self
    {
        try {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['connections']['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";
            $pdo = new \PDO($dsn, $mysql['username'], $mysql['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false
            ]);

            $stmt = $pdo->prepare('SELECT * FROM admin_users WHERE username = ?');
            $stmt->execute([$username]);
            $row = $stmt->fetch();

            if (!$row) {
                return null;
            }

            return new self(
                (int) $row['id'],
                $row['username'],
                $row['password_hash'], // Use 'password_hash' column name as per actual MySQL table structure
                $row['email'],
                new DateTimeImmutable($row['created_at']),
                new DateTimeImmutable($row['updated_at'])
            );
        } catch (\Exception $e) {
            return null;
        }
    }

    public function updatePassword(string $newPasswordHash): bool
    {
        try {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['connections']['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";
            $pdo = new \PDO($dsn, $mysql['username'], $mysql['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false
            ]);

            $stmt = $pdo->prepare('UPDATE admin_users SET password_hash = ?, updated_at = NOW() WHERE id = ?');
            return $stmt->execute([$newPasswordHash, $this->id]);
        } catch (\Exception $e) {
            return false;
        }
    }

    public function updateProfile(string $newUsername, string $newEmail): bool
    {
        try {
            $config = require dirname(__DIR__, 2) . '/config/database.php';
            $mysql = $config['connections']['mysql'];

            $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset=utf8mb4";
            $pdo = new \PDO($dsn, $mysql['username'], $mysql['password'], [
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                \PDO::ATTR_EMULATE_PREPARES => false
            ]);

            $stmt = $pdo->prepare('UPDATE admin_users SET username = ?, email = ?, updated_at = NOW() WHERE id = ?');
            $result = $stmt->execute([$newUsername, $newEmail, $this->id]);

            if ($result) {
                // Update the object properties
                $this->username = $newUsername;
                $this->email = $newEmail;
                $this->updatedAt = new DateTimeImmutable();
            }

            return $result;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->passwordHash);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getPasswordHash(): string
    {
        return $this->passwordHash;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),
            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),
        ];
    }
}
