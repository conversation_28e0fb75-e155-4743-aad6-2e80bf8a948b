<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Settings;
use App\Models\AdminUser;
use App\Services\StorageService;

/**
 * Admin Controller
 * Handles admin panel API endpoints
 */
final class AdminController extends Controller
{
    private StorageService $storageService;

    public function __construct(StorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    public function getSettings(): array
    {
        try {
            // Get admin user data from database
            $adminUser = null;
            $username = $_SESSION['admin_user'] ?? 'admin';

            if ($username) {
                $adminUser = AdminUser::findByUsername($username);
            }

            // Get other settings from Settings model
            $settings = Settings::getAll();

            // Convert to simple key-value format for frontend
            $settingsData = [];
            foreach ($settings as $key => $value) {
                $settingsData[$key] = $value;
            }

            // Provide default values if settings don't exist
            $defaultSettings = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'notification_email' => '<EMAIL>',
                'site_name' => 'DrxDion',
                'timezone' => 'Europe/Istanbul'
            ];

            // Merge with defaults first, then override with actual settings
            $result = array_merge($defaultSettings, $settingsData);

            // Override admin data with database values if available
            if ($adminUser) {
                $result['username'] = $adminUser->getUsername();
                $result['email'] = $adminUser->getEmail();
            }

            return [
                'success' => true,
                'data' => $result
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to load settings: ' . $e->getMessage()
            ];
        }
    }

    public function updateSettings(array $data): array
    {
        try {
            if (empty($data)) {
                return [
                    'success' => false,
                    'error' => 'No data provided'
                ];
            }

            // Get current admin user
            $currentUsername = $_SESSION['admin_user'] ?? 'admin';
            $currentUser = AdminUser::findByUsername($currentUsername);

            if (!$currentUser) {
                return [
                    'success' => false,
                    'error' => 'Admin user not found'
                ];
            }

            // Handle admin profile updates (username and email)
            $adminDataChanged = false;
            if (isset($data['username']) || isset($data['email'])) {
                $newUsername = $data['username'] ?? $currentUser->getUsername();
                $newEmail = $data['email'] ?? $currentUser->getEmail();

                // Verify current password for profile changes
                if (empty($data['currentPassword'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is required to update profile'
                    ];
                }

                if (!$currentUser->verifyPassword($data['currentPassword'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ];
                }

                // Update admin profile in database
                if (!$currentUser->updateProfile($newUsername, $newEmail)) {
                    return [
                        'success' => false,
                        'error' => 'Failed to update admin profile'
                    ];
                }

                // Update session if username changed
                if ($newUsername !== $currentUsername) {
                    $_SESSION['admin_user'] = $newUsername;
                }

                $adminDataChanged = true;
            }

            // Handle password update separately if provided
            if (!empty($data['newPassword'])) {
                if (empty($data['currentPassword'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is required to change password'
                    ];
                }

                // Verify current password (if not already verified above)
                if (!$adminDataChanged && !$currentUser->verifyPassword($data['currentPassword'])) {
                    return [
                        'success' => false,
                        'error' => 'Current password is incorrect'
                    ];
                }

                // Update password
                $hashedPassword = password_hash($data['newPassword'], PASSWORD_DEFAULT);
                if (!$currentUser->updatePassword($hashedPassword)) {
                    return [
                        'success' => false,
                        'error' => 'Failed to update password'
                    ];
                }
            }

            // Update other settings (notification email, site settings, etc.)
            $settingsToUpdate = [];
            $envToUpdate = [];
            $allowedSettings = ['notification_email', 'site_name', 'timezone'];

            foreach ($allowedSettings as $key) {
                if (isset($data[$key])) {
                    $settingsToUpdate[$key] = $data[$key];

                    // Also update .env file for notification email only
                    if ($key === 'notification_email') {
                        $envToUpdate['NOTIFICATION_EMAIL'] = $data[$key];
                        $envToUpdate['ORDER_NOTIFICATION_TO'] = $data[$key]; // Backward compatibility
                    }
                }
            }

            // Update .env file first
            if (!empty($envToUpdate)) {
                \App\Utils\EnvManager::setMultiple($envToUpdate);
            }

            // Update settings storage
            if (!empty($settingsToUpdate)) {
                Settings::updateMultiple($settingsToUpdate);
            }

            return [
                'success' => true,
                'message' => 'Settings updated successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to update settings: ' . $e->getMessage()
            ];
        }
    }

    public function getStorageStatus(): array
    {
        try {
            $status = $this->storageService->getStatus();
            $healthMetrics = $this->storageService->getHealthMetrics();
            $syncStats = $this->storageService->getSyncStats();

            return [
                'success' => true,
                'data' => [
                    'status' => $status,
                    'health' => $healthMetrics,
                    'sync' => $syncStats
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get storage status: ' . $e->getMessage()
            ];
        }
    }

    public function syncStorage(array $data): array
    {
        try {
            // CSRF protection
            if (!$this->validateCSRF()) {
                return [
                    'success' => false,
                    'error' => 'Invalid CSRF token'
                ];
            }

            $forceReplace = ($data['force_replace'] ?? false) === true;

            $result = $this->storageService->sync($forceReplace);

            if ($result['success']) {
                return [
                    'success' => true,
                    'message' => 'Storage sync completed successfully',
                    'data' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'] ?? 'Sync failed',
                    'data' => $result
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to sync storage: ' . $e->getMessage()
            ];
        }
    }

    public function login(): void
    {
        header('Content-Type: application/json');

        try {
            // Get POST data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (!$data) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid JSON data']);
                return;
            }

            $username = $data['username'] ?? '';
            $password = $data['password'] ?? '';

            // Simple hardcoded admin credentials for now
            if ($username === 'admin' && $password === 'admin123') {
                // Set session
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_user'] = $username;
                $_SESSION['login_time'] = time();

                echo json_encode([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => '/admin/orders'
                ]);
            } else {
                http_response_code(401);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid username or password'
                ]);
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error: ' . $e->getMessage()
            ]);
        }
    }

    public function logout(): void
    {
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        try {
            // Destroy session
            session_destroy();

            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                // AJAX or POST request - return JSON
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Logout successful',
                    'redirect' => '/admin/login'
                ]);
            } else {
                // GET request - redirect directly
                header('Location: /admin/login');
                exit;
            }
        } catch (\Exception $e) {
            if ($isAjax || $_SERVER['REQUEST_METHOD'] === 'POST') {
                header('Content-Type: application/json');
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Server error: ' . $e->getMessage()
                ]);
            } else {
                header('Location: /admin/login?error=logout_failed');
                exit;
            }
        }
    }

    private function validateCSRF(): bool
    {
        $providedToken = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        $sessionToken = $_SESSION['csrf_token'] ?? '';

        return !empty($providedToken) && !empty($sessionToken) && hash_equals($sessionToken, $providedToken);
    }
}
